import Link from 'next/link';
import Layout from "@/components/layout/Layout";

export default function NotFound() {
  return (
    <Layout>
      <div className="min-h-screen bg-brand-cream flex items-center justify-center py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-8">
              <h1 className="font-serif text-6xl md:text-8xl font-bold text-brand-charcoal mb-4">
                404
              </h1>
              <h2 className="font-serif text-2xl md:text-3xl font-bold text-brand-charcoal mb-4">
                Page Not Found
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
              </p>
            </div>

            <div className="space-y-4 mb-8">
              <Link
                href="/"
                className="inline-block bg-brand-gold text-white px-8 py-3 rounded-md hover:opacity-90 transition-opacity font-medium"
              >
                Go Home
              </Link>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/products"
                  className="text-brand-charcoal hover:text-brand-gold transition-colors font-medium"
                >
                  Browse Products
                </Link>
                <Link
                  href="/about"
                  className="text-brand-charcoal hover:text-brand-gold transition-colors font-medium"
                >
                  About Us
                </Link>
                <Link
                  href="/blog"
                  className="text-brand-charcoal hover:text-brand-gold transition-colors font-medium"
                >
                  Read Our Blog
                </Link>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm">
              <h3 className="font-serif text-xl font-bold text-brand-charcoal mb-4">
                Popular Categories
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Link
                  href="/products?category=handbags"
                  className="text-gray-600 hover:text-brand-gold transition-colors"
                >
                  Handbags
                </Link>
                <Link
                  href="/products?category=caps"
                  className="text-gray-600 hover:text-brand-gold transition-colors"
                >
                  Caps
                </Link>
                <Link
                  href="/products?category=earrings"
                  className="text-gray-600 hover:text-brand-gold transition-colors"
                >
                  Earrings
                </Link>
                <Link
                  href="/products?category=custom"
                  className="text-gray-600 hover:text-brand-gold transition-colors"
                >
                  Custom Designs
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
