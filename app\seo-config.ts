// SEO Configuration for Dreamy Duffel
export const seoConfig = {
  siteName: 'Dreamy Duffel',
  siteUrl: 'https://dreamyduffles.shop',
  defaultTitle: 'Dreamy Duffel - Premium Handbags & Caps | Custom Designs Nepal',
  defaultDescription: 'Discover premium handbags and caps at Dreamy Duffel, including customized designs tailored to your style. Shop quality accessories in Nepal with fast delivery.',
  defaultKeywords: 'handbags, caps, custom designs, premium accessories, Dreamy Duffel, Nepal, fashion accessories',
  defaultImage: '/og-image.jpg',
  twitterHandle: '@dreamyduffel',
  locale: 'en_NP',
  country: 'Nepal',
  region: 'NP',
  
  // Business Information
  business: {
    name: 'Dreamy Duffel',
    description: 'Premium handbags and caps with custom designs in Nepal',
    address: {
      streetAddress: '',
      addressLocality: 'Kathmandu',
      addressRegion: 'Bagmati',
      postalCode: '',
      addressCountry: 'Nepal',
    },
    contactPoint: {
      telephone: '+977-XXXXXXXXX',
      contactType: 'customer service',
      email: '<EMAIL>',
    },
    sameAs: [
      'https://www.facebook.com/dreamyduffel',
      'https://www.instagram.com/dreamyduffel',
      'https://www.twitter.com/dreamyduffel',
    ],
  },
  
  // Product Categories for SEO
  categories: [
    {
      name: 'Handbags',
      slug: 'handbags',
      description: 'Premium handbags crafted with attention to detail and style',
      keywords: 'handbags, premium bags, designer bags, Nepal handbags',
    },
    {
      name: 'Caps',
      slug: 'caps',
      description: 'Stylish caps perfect for any occasion',
      keywords: 'caps, hats, fashion caps, custom caps, Nepal caps',
    },
    {
      name: 'Hair Bands',
      slug: 'hairbands',
      description: 'Elegant hair bands that add sophistication to your hairstyle',
      keywords: 'hair bands, hair accessories, elegant hairbands, Nepal hair accessories',
    },
    {
      name: 'Earrings',
      slug: 'earrings',
      description: 'Beautiful earrings for every occasion',
      keywords: 'earrings, jewelry, fashion earrings, Nepal jewelry',
    },
    {
      name: 'Necklaces',
      slug: 'necklaces',
      description: 'Exquisite necklaces that complement your style perfectly',
      keywords: 'necklaces, jewelry, fashion necklaces, Nepal jewelry',
    },
    {
      name: 'Silk Pillow Covers',
      slug: 'pillowcovers',
      description: 'Luxury silk pillow covers for better sleep and comfort',
      keywords: 'silk pillow covers, luxury bedding, silk pillowcases, Nepal silk',
    },
    {
      name: 'Custom Designs',
      slug: 'custom',
      description: 'Personalized accessories with custom design options',
      keywords: 'custom designs, personalized accessories, custom handbags, custom caps',
    },
  ],
};

// Generate page-specific SEO data
export function generatePageSEO(page: string, customData?: Partial<typeof seoConfig>) {
  const config = { ...seoConfig, ...customData };
  
  return {
    title: config.defaultTitle,
    description: config.defaultDescription,
    keywords: config.defaultKeywords,
    openGraph: {
      title: config.defaultTitle,
      description: config.defaultDescription,
      url: `${config.siteUrl}${page === 'home' ? '' : `/${page}`}`,
      siteName: config.siteName,
      images: [
        {
          url: config.defaultImage,
          width: 1200,
          height: 630,
          alt: config.siteName,
        },
      ],
      locale: config.locale,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      site: config.twitterHandle,
      creator: config.twitterHandle,
      title: config.defaultTitle,
      description: config.defaultDescription,
      images: [config.defaultImage],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    alternates: {
      canonical: `${config.siteUrl}${page === 'home' ? '' : `/${page}`}`,
    },
  };
}

// Generate product-specific SEO data
export function generateProductSEO(product: any) {
  return {
    title: `${product.name} | ${seoConfig.siteName}`,
    description: product.description,
    keywords: `${product.name}, ${product.category}, premium accessories, ${seoConfig.siteName}, Nepal`,
    openGraph: {
      title: `${product.name} | ${seoConfig.siteName}`,
      description: product.description,
      url: `${seoConfig.siteUrl}/products/${product.id}`,
      images: [
        {
          url: product.images[0],
          width: 800,
          height: 600,
          alt: product.name,
        },
      ],
      type: 'website',
    },
    twitter: {
      title: `${product.name} | ${seoConfig.siteName}`,
      description: product.description,
      images: [product.images[0]],
    },
    alternates: {
      canonical: `${seoConfig.siteUrl}/products/${product.id}`,
    },
  };
}

// Generate blog post SEO data
export function generateBlogSEO(post: any) {
  return {
    title: `${post.title} | ${seoConfig.siteName} Blog`,
    description: post.excerpt,
    keywords: `${post.tags?.join(', ')}, fashion blog, style tips, ${seoConfig.siteName}`,
    openGraph: {
      title: `${post.title} | ${seoConfig.siteName} Blog`,
      description: post.excerpt,
      url: `${seoConfig.siteUrl}/blog/${post.slug}`,
      images: [
        {
          url: post.featuredImage,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
      type: 'article',
      publishedTime: post.publishedAt,
      modifiedTime: post.updatedAt,
      authors: [post.author],
      section: post.category,
      tags: post.tags,
    },
    twitter: {
      title: `${post.title} | ${seoConfig.siteName} Blog`,
      description: post.excerpt,
      images: [post.featuredImage],
    },
    alternates: {
      canonical: `${seoConfig.siteUrl}/blog/${post.slug}`,
    },
  };
}
