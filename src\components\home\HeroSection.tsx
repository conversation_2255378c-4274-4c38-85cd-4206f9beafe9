
import Link from "next/link";

const HeroSection = () => {
  return (
    <div className="relative bg-gray-900 text-white">
      <div
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1623834574742-acf70c27b5c5?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80')",
          backgroundSize: "cover",
          backgroundPosition: "center",
          opacity: 0.7
        }}
      ></div>
      <div className="relative z-10 container mx-auto px-4 py-24 md:py-32 flex flex-col items-center text-center">
        <h1 className="font-serif text-4xl md:text-6xl font-bold mb-6 leading-tight">
          Elevate Your Style With Elegance
        </h1>
        <p className="max-w-2xl text-lg md:text-xl mb-8">
          Discover our collection of premium handbags, caps, jewelry, hair accessories, and luxury silk pillow covers, including custom designs tailored to your personal style.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Link href="/products" className="btn-primary text-center">
            Shop Collection
          </Link>
          <Link href="/products?category=custom" className="btn-outline text-center bg-white/80 text-brand-charcoal">
            Custom Designs
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
