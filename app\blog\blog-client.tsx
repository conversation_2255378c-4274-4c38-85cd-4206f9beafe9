'use client';

import { useState, useEffect } from "react";
import Layout from "@/components/layout/Layout";
import { BlogPost } from "@/data/blog";
import { dataService } from "@/lib/dataService";
import Link from "next/link";
import { Calendar, Clock, User, Tag } from "lucide-react";
import StructuredData from "@/components/seo/StructuredData";

export default function BlogClient() {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [allPosts, setAllPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const filteredPosts = selectedCategory === "all"
    ? allPosts
    : allPosts.filter(post => post.category === selectedCategory);

  useEffect(() => {
    const loadBlogData = async () => {
      try {
        setLoading(true);
        setError(null);

        const [posts, blogCategories] = await Promise.all([
          dataService.blog.getPublished(),
          dataService.blog.getCategories()
        ]);

        setAllPosts(posts);
        setCategories(blogCategories);
      } catch (err) {
        console.error('Failed to load blog data:', err);
        setError('Failed to load blog posts');
      } finally {
        setLoading(false);
      }
    };

    loadBlogData();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getReadingTime = (content: string) => {
    const wordsPerMinute = 200;
    const wordCount = content.split(' ').length;
    return Math.ceil(wordCount / wordsPerMinute);
  };

  return (
    <Layout>
      <StructuredData type="website" />
      
      {/* Hero Section */}
      <div className="bg-brand-cream py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="font-serif text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
              Our Blog
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Discover the latest fashion trends, styling tips, and behind-the-scenes stories from Dreamy Duffel.
            </p>
          </div>
        </div>
      </div>

      {/* Blog Content */}
      <div className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="lg:w-2/3">
              {loading ? (
                <div className="flex justify-center items-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-gold"></div>
                  <span className="ml-3 text-gray-600">Loading blog posts...</span>
                </div>
              ) : error ? (
                <div className="text-center py-12">
                  <p className="text-red-600 mb-4">{error}</p>
                  <button
                    onClick={() => window.location.reload()}
                    className="btn-primary"
                  >
                    Try Again
                  </button>
                </div>
              ) : filteredPosts.length > 0 ? (
                <div className="space-y-8">
                  {filteredPosts.map((post) => (
                    <article key={post.id} className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                      <div className="md:flex">
                        <div className="md:w-1/3">
                          <img
                            src={post.featuredImage}
                            alt={post.title}
                            className="w-full h-48 md:h-full object-cover"
                          />
                        </div>
                        <div className="md:w-2/3 p-6">
                          <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                            <div className="flex items-center space-x-1">
                              <Calendar size={14} />
                              <span>{formatDate(post.publishedAt)}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock size={14} />
                              <span>{getReadingTime(post.content)} min read</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <User size={14} />
                              <span>{post.author}</span>
                            </div>
                          </div>
                          
                          <h2 className="font-serif text-xl md:text-2xl font-bold text-brand-charcoal mb-3 hover:text-brand-gold transition-colors">
                            <Link href={`/blog/${post.slug}`}>
                              {post.title}
                            </Link>
                          </h2>
                          
                          <p className="text-gray-600 mb-4 line-clamp-3">
                            {post.excerpt}
                          </p>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <Tag size={14} className="text-gray-400" />
                              <span className="text-sm text-brand-gold font-medium capitalize">
                                {post.category}
                              </span>
                            </div>
                            <Link
                              href={`/blog/${post.slug}`}
                              className="text-brand-gold hover:text-brand-charcoal font-medium transition-colors"
                            >
                              Read More →
                            </Link>
                          </div>
                        </div>
                      </div>
                    </article>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-600 mb-4">No blog posts found in this category.</p>
                  <button
                    onClick={() => setSelectedCategory("all")}
                    className="btn-primary"
                  >
                    View All Posts
                  </button>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:w-1/3">
              <div className="bg-brand-light p-6 rounded-lg">
                <h3 className="font-serif text-xl font-bold text-brand-charcoal mb-4">
                  Categories
                </h3>
                <div className="space-y-2">
                  <button
                    onClick={() => setSelectedCategory("all")}
                    className={`block w-full text-left px-3 py-2 rounded-md transition-colors ${
                      selectedCategory === "all"
                        ? "bg-brand-gold text-white"
                        : "text-gray-600 hover:bg-white"
                    }`}
                  >
                    All Posts ({allPosts.length})
                  </button>
                  {categories.map((category) => {
                    const count = allPosts.filter(post => post.category === category).length;
                    return (
                      <button
                        key={category}
                        onClick={() => setSelectedCategory(category)}
                        className={`block w-full text-left px-3 py-2 rounded-md transition-colors capitalize ${
                          selectedCategory === category
                            ? "bg-brand-gold text-white"
                            : "text-gray-600 hover:bg-white"
                        }`}
                      >
                        {category} ({count})
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Recent Posts */}
              <div className="bg-brand-light p-6 rounded-lg mt-6">
                <h3 className="font-serif text-xl font-bold text-brand-charcoal mb-4">
                  Recent Posts
                </h3>
                <div className="space-y-4">
                  {allPosts.slice(0, 3).map((post) => (
                    <div key={post.id} className="flex space-x-3">
                      <img
                        src={post.featuredImage}
                        alt={post.title}
                        className="w-16 h-16 object-cover rounded-md"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-brand-charcoal text-sm mb-1 line-clamp-2">
                          <Link href={`/blog/${post.slug}`} className="hover:text-brand-gold transition-colors">
                            {post.title}
                          </Link>
                        </h4>
                        <p className="text-xs text-gray-500">
                          {formatDate(post.publishedAt)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Newsletter Signup */}
              <div className="bg-brand-charcoal text-white p-6 rounded-lg mt-6">
                <h3 className="font-serif text-xl font-bold mb-4">
                  Stay Updated
                </h3>
                <p className="text-gray-300 mb-4">
                  Subscribe to our newsletter for the latest fashion tips and product updates.
                </p>
                <div className="space-y-3">
                  <input
                    type="email"
                    placeholder="Your email address"
                    className="w-full px-3 py-2 bg-white text-brand-charcoal rounded-md focus:outline-none"
                  />
                  <button className="w-full bg-brand-gold hover:opacity-90 transition-opacity px-4 py-2 rounded-md">
                    Subscribe
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
