
import Link from "next/link";
import { Facebook, Instagram, Twitter } from "lucide-react";
import { PiTiktokLogoLight } from "react-icons/pi";

const Footer = () => {
  return (
    <footer className="bg-brand-charcoal text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="font-serif text-xl font-semibold mb-4">Dreamy Duffel</h3>
            <p className="text-sm text-gray-300 mb-4">
              Elevate your style with our premium collection of handbags and caps.
              From elegant designs to customized pieces that reflect your personality.
            </p>
            <div className="flex space-x-4">
              <a href="https://www.facebook.com/profile.php?id=100092000000000" className="text-white hover:text-brand-gold transition-colors">
                <Facebook size={20} />
              </a>
              <a href="https://www.instagram.com/dreamy_duffel/ " className="text-white hover:text-brand-gold transition-colors">
                <Instagram size={20} />
              </a>
              <a href="#" className="text-white hover:text-brand-gold transition-colors">
                <PiTiktokLogoLight size={20}/>
              </a>
            </div>
          </div>

          <div>
            <h3 className="font-serif text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-300 hover:text-brand-gold transition-colors text-sm">
                  Home
                </Link>
              </li>
              <li>
                <Link href="/products" className="text-gray-300 hover:text-brand-gold transition-colors text-sm">
                  Products
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-300 hover:text-brand-gold transition-colors text-sm">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/return-policy" className="text-gray-300 hover:text-brand-gold transition-colors text-sm">
                  Return Policy
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-serif text-lg font-semibold mb-4">Categories</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/products?category=handbags" className="text-gray-300 hover:text-brand-gold transition-colors text-sm">
                  Handbags
                </Link>
              </li>
              <li>
                <Link href="/products?category=caps" className="text-gray-300 hover:text-brand-gold transition-colors text-sm">
                  Caps
                </Link>
              </li>
              <li>
                <Link href="/products?category=custom" className="text-gray-300 hover:text-brand-gold transition-colors text-sm">
                  Customized Items
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="font-serif text-lg font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-2 text-sm text-gray-300">
              <li>Email: <EMAIL></li>
              <li>Phone: +977-9816357185</li>
              <li>Address: Biratnagar, Morang, Nepal</li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-gray-400">
            &copy; {new Date().getFullYear()} Dreamy Duffel. All rights reserved.
          </p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <Link href="/privacy-policy" className="text-sm text-gray-400 hover:text-brand-gold transition-colors">
              Privacy Policy
            </Link>
            <Link href="/terms-of-service" className="text-sm text-gray-400 hover:text-brand-gold transition-colors">
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
