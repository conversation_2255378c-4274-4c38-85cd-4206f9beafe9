'use client';

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import Layout from "@/components/layout/Layout";
import { Product } from "@/data/products";
import { ShoppingBag, Heart, ChevronRight, ChevronLeft, Check, MinusCircle, PlusCircle } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ProductCard from "@/components/product/ProductCard";
import { formatPrice } from "@/lib/utils";
import StructuredData from "@/components/seo/StructuredData";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";

interface ProductDetailClientProps {
  product: Product;
  relatedProducts: Product[];
}

export default function ProductDetailClient({ product, relatedProducts }: ProductDetailClientProps) {
  const [mainImage, setMainImage] = useState(product.images[0]);
  const [quantity, setQuantity] = useState(1);
  const [customText, setCustomText] = useState("");

  const handleQuantityChange = (change: number) => {
    setQuantity(Math.max(1, quantity + change));
  };

  const handleAddToCart = () => {
    // Add to cart logic here
    console.log('Adding to cart:', { product, quantity, customText });
  };

  const handleAddToWishlist = () => {
    // Add to wishlist logic here
    console.log('Adding to wishlist:', product);
  };

  return (
    <Layout>
      <StructuredData 
        type="product" 
        data={{
          name: product.name,
          description: product.description,
          image: product.images,
          price: product.price,
          currency: "INR",
          availability: product.inStock ? "in stock" : "out of stock",
          condition: "new",
          brand: "Dreamy Duffel",
          category: product.category,
          sku: product.id,
          url: `https://dreamyduffles.shop/products/${product.id}`
        }}
      />
      
      <div className="bg-brand-cream py-6">
        <div className="container mx-auto px-4">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/">Home</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/products">Products</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href={`/products?category=${product.category}`}>
                    {product.category.charAt(0).toUpperCase() + product.category.slice(1)}
                  </Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{product.name}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </div>

      <div className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Product Images */}
            <div className="space-y-4">
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <Image
                  src={mainImage}
                  alt={product.name}
                  width={600}
                  height={600}
                  className="w-full h-full object-cover"
                />
              </div>
              
              {product.images.length > 1 && (
                <div className="grid grid-cols-4 gap-2">
                  {product.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setMainImage(image)}
                      className={`aspect-square bg-gray-100 rounded-md overflow-hidden border-2 ${
                        mainImage === image ? 'border-brand-gold' : 'border-transparent'
                      }`}
                    >
                      <Image
                        src={image}
                        alt={`${product.name} ${index + 1}`}
                        width={150}
                        height={150}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Product Info */}
            <div className="space-y-6">
              <div>
                <h1 className="font-serif text-3xl md:text-4xl font-bold text-brand-charcoal mb-2">
                  {product.name}
                </h1>
                <p className="text-2xl font-bold text-brand-gold">
                  {formatPrice(product.price)}
                </p>
                {product.originalPrice && product.originalPrice > product.price && (
                  <p className="text-lg text-gray-500 line-through">
                    {formatPrice(product.originalPrice)}
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <span
                      key={i}
                      className={`text-lg ${
                        i < (product.rating || 0) ? 'text-yellow-400' : 'text-gray-300'
                      }`}
                    >
                      ★
                    </span>
                  ))}
                </div>
                <span className="text-gray-600">({product.rating || 0}/5)</span>
              </div>

              <p className="text-gray-600 leading-relaxed">
                {product.description}
              </p>

              {/* Customization Options */}
              {product.customizable && (
                <div className="bg-brand-light p-4 rounded-lg">
                  <h3 className="font-serif text-lg font-bold text-brand-charcoal mb-2">
                    Customize Your Product
                  </h3>
                  <p className="text-gray-600 mb-3">
                    Add personalized text to make this item uniquely yours.
                  </p>
                  <input
                    type="text"
                    placeholder="Enter custom text (optional)"
                    value={customText}
                    onChange={(e) => setCustomText(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand-gold"
                    maxLength={50}
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    {customText.length}/50 characters
                  </p>
                </div>
              )}

              {/* Quantity and Add to Cart */}
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <span className="font-medium text-brand-charcoal">Quantity:</span>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleQuantityChange(-1)}
                      className="p-1 rounded-full hover:bg-gray-100"
                      disabled={quantity <= 1}
                    >
                      <MinusCircle size={20} className={quantity <= 1 ? 'text-gray-300' : 'text-brand-charcoal'} />
                    </button>
                    <span className="w-12 text-center font-medium">{quantity}</span>
                    <button
                      onClick={() => handleQuantityChange(1)}
                      className="p-1 rounded-full hover:bg-gray-100"
                    >
                      <PlusCircle size={20} className="text-brand-charcoal" />
                    </button>
                  </div>
                </div>

                <div className="flex space-x-4">
                  <button
                    onClick={handleAddToCart}
                    className="flex-1 bg-brand-gold text-white px-6 py-3 rounded-md hover:opacity-90 transition-opacity flex items-center justify-center space-x-2"
                    disabled={!product.inStock}
                  >
                    <ShoppingBag size={20} />
                    <span>{product.inStock ? 'Add to Cart' : 'Out of Stock'}</span>
                  </button>
                  <button
                    onClick={handleAddToWishlist}
                    className="px-4 py-3 border border-brand-gold text-brand-charcoal rounded-md hover:bg-brand-cream transition-colors"
                  >
                    <Heart size={20} />
                  </button>
                </div>
              </div>

              {/* Product Features */}
              <div className="border-t border-gray-200 pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    <Check size={16} className="text-green-600" />
                    <span className="text-gray-600">Premium Quality</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Check size={16} className="text-green-600" />
                    <span className="text-gray-600">Fast Delivery</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Check size={16} className="text-green-600" />
                    <span className="text-gray-600">Easy Returns</span>
                  </div>
                  {product.customizable && (
                    <div className="flex items-center space-x-2">
                      <Check size={16} className="text-green-600" />
                      <span className="text-gray-600">Customizable</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Product Details Tabs */}
      <div className="py-12 bg-brand-light">
        <div className="container mx-auto px-4">
          <Tabs defaultValue="description" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="description">Description</TabsTrigger>
              <TabsTrigger value="specifications">Specifications</TabsTrigger>
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
            </TabsList>
            
            <TabsContent value="description" className="mt-6">
              <div className="bg-white p-6 rounded-lg">
                <h3 className="font-serif text-xl font-bold text-brand-charcoal mb-4">
                  Product Description
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {product.description}
                </p>
                {product.features && product.features.length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-medium text-brand-charcoal mb-2">Features:</h4>
                    <ul className="list-disc list-inside space-y-1">
                      {product.features.map((feature, index) => (
                        <li key={index} className="text-gray-600">{feature}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="specifications" className="mt-6">
              <div className="bg-white p-6 rounded-lg">
                <h3 className="font-serif text-xl font-bold text-brand-charcoal mb-4">
                  Specifications
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="font-medium text-brand-charcoal">Category:</span>
                    <span className="ml-2 text-gray-600 capitalize">{product.category}</span>
                  </div>
                  <div>
                    <span className="font-medium text-brand-charcoal">Material:</span>
                    <span className="ml-2 text-gray-600">{product.material || 'Premium Quality'}</span>
                  </div>
                  <div>
                    <span className="font-medium text-brand-charcoal">Customizable:</span>
                    <span className="ml-2 text-gray-600">{product.customizable ? 'Yes' : 'No'}</span>
                  </div>
                  <div>
                    <span className="font-medium text-brand-charcoal">Availability:</span>
                    <span className="ml-2 text-gray-600">{product.inStock ? 'In Stock' : 'Out of Stock'}</span>
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="reviews" className="mt-6">
              <div className="bg-white p-6 rounded-lg">
                <h3 className="font-serif text-xl font-bold text-brand-charcoal mb-4">
                  Customer Reviews
                </h3>
                <p className="text-gray-600">
                  Reviews feature coming soon. Be the first to review this product!
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Related Products */}
      {relatedProducts.length > 0 && (
        <div className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <h2 className="font-serif text-3xl font-bold text-brand-charcoal text-center mb-12">
              Related Products
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <ProductCard key={relatedProduct.id} product={relatedProduct} />
              ))}
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
}
