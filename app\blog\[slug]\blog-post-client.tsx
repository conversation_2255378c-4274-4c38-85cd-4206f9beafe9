'use client';

import Link from "next/link";
import Layout from "@/components/layout/Layout";
import { BlogPost } from "@/data/blog";
import { Calendar, Clock, User, Tag, ArrowLeft } from "lucide-react";
import StructuredData from "@/components/seo/StructuredData";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";

interface BlogPostClientProps {
  post: BlogPost;
  relatedPosts: BlogPost[];
}

export default function BlogPostClient({ post, relatedPosts }: BlogPostClientProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getReadingTime = (content: string) => {
    const wordsPerMinute = 200;
    const wordCount = content.split(' ').length;
    return Math.ceil(wordCount / wordsPerMinute);
  };

  return (
    <Layout>
      <StructuredData 
        type="article" 
        data={{
          headline: post.title,
          description: post.excerpt,
          image: post.featuredImage,
          author: post.author,
          publishedTime: post.publishedAt,
          modifiedTime: post.updatedAt,
          section: post.category,
          tags: post.tags,
          url: `https://dreamyduffles.shop/blog/${post.slug}`
        }}
      />
      
      {/* Breadcrumb */}
      <div className="bg-brand-cream py-6">
        <div className="container mx-auto px-4">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/">Home</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href="/blog">Blog</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>{post.title}</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </div>

      {/* Article Content */}
      <article className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Back to Blog */}
            <Link
              href="/blog"
              className="inline-flex items-center text-brand-gold hover:text-brand-charcoal transition-colors mb-8"
            >
              <ArrowLeft size={16} className="mr-2" />
              Back to Blog
            </Link>

            {/* Article Header */}
            <header className="mb-8">
              <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
                <div className="flex items-center space-x-1">
                  <Calendar size={14} />
                  <span>{formatDate(post.publishedAt)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock size={14} />
                  <span>{getReadingTime(post.content)} min read</span>
                </div>
                <div className="flex items-center space-x-1">
                  <User size={14} />
                  <span>{post.author}</span>
                </div>
              </div>

              <h1 className="font-serif text-3xl md:text-4xl lg:text-5xl font-bold text-brand-charcoal mb-4">
                {post.title}
              </h1>

              <p className="text-lg text-gray-600 mb-6">
                {post.excerpt}
              </p>

              <div className="flex items-center space-x-2 mb-6">
                <Tag size={14} className="text-gray-400" />
                <span className="text-sm text-brand-gold font-medium capitalize">
                  {post.category}
                </span>
                {post.tags && post.tags.length > 0 && (
                  <>
                    <span className="text-gray-400">•</span>
                    <div className="flex flex-wrap gap-2">
                      {post.tags.map((tag, index) => (
                        <span key={index} className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </>
                )}
              </div>

              {/* Featured Image */}
              <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden mb-8">
                <img
                  src={post.featuredImage}
                  alt={post.title}
                  className="w-full h-full object-cover"
                />
              </div>
            </header>

            {/* Article Content */}
            <div className="prose prose-lg max-w-none">
              <div 
                className="text-gray-700 leading-relaxed"
                dangerouslySetInnerHTML={{ __html: post.content }}
              />
            </div>

            {/* Article Footer */}
            <footer className="mt-12 pt-8 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <span className="text-gray-600">Share this article:</span>
                  <div className="flex space-x-2">
                    <button className="text-gray-400 hover:text-brand-gold transition-colors">
                      Facebook
                    </button>
                    <button className="text-gray-400 hover:text-brand-gold transition-colors">
                      Twitter
                    </button>
                    <button className="text-gray-400 hover:text-brand-gold transition-colors">
                      LinkedIn
                    </button>
                  </div>
                </div>
                <div className="text-sm text-gray-500">
                  Last updated: {formatDate(post.updatedAt)}
                </div>
              </div>
            </footer>
          </div>
        </div>
      </article>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <section className="py-16 bg-brand-light">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="font-serif text-2xl md:text-3xl font-bold text-brand-charcoal mb-8">
                Related Articles
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {relatedPosts.map((relatedPost) => (
                  <article key={relatedPost.id} className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <img
                      src={relatedPost.featuredImage}
                      alt={relatedPost.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="p-4">
                      <div className="text-sm text-gray-500 mb-2">
                        {formatDate(relatedPost.publishedAt)}
                      </div>
                      <h3 className="font-serif text-lg font-bold text-brand-charcoal mb-2 line-clamp-2">
                        <Link href={`/blog/${relatedPost.slug}`} className="hover:text-brand-gold transition-colors">
                          {relatedPost.title}
                        </Link>
                      </h3>
                      <p className="text-gray-600 text-sm line-clamp-3 mb-3">
                        {relatedPost.excerpt}
                      </p>
                      <Link
                        href={`/blog/${relatedPost.slug}`}
                        className="text-brand-gold hover:text-brand-charcoal font-medium text-sm transition-colors"
                      >
                        Read More →
                      </Link>
                    </div>
                  </article>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* Newsletter CTA */}
      <section className="py-16 bg-brand-charcoal text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center">
            <h2 className="font-serif text-2xl md:text-3xl font-bold mb-4">
              Stay Updated with Our Latest Posts
            </h2>
            <p className="text-gray-300 mb-6">
              Subscribe to our newsletter for the latest fashion tips, product updates, and exclusive content.
            </p>
            <div className="flex flex-col sm:flex-row gap-2">
              <input
                type="email"
                placeholder="Your email address"
                className="flex-grow px-4 py-2 bg-white text-brand-charcoal rounded-md focus:outline-none"
              />
              <button className="bg-brand-gold hover:opacity-90 transition-opacity px-6 py-2 rounded-md">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </section>
    </Layout>
  );
}
