export interface Product {
  id: string;
  name: string;
  category:
    | "handbags"
    | "caps"
    | "custom"
    | "hairbands"
    | "earrings"
    | "necklaces"
    | "pillowcovers";
  price: number;
  description: string;
  features: string[];
  images: string[];
  isCustomizable: boolean;
  inStock: boolean;
  isFeatured?: boolean;
}

export const products: Product[] = [
  {
    id: "handbag-1",
    name: "Elegant Tote Bag",
    category: "handbags",
    price: 10799,
    description:
      "A sophisticated tote bag perfect for daily use. Made from premium materials with ample storage space for all your essentials.",
    features: [
      "Premium synthetic leather",
      "Gold-tone hardware",
      "Interior zip pocket",
      "Adjustable shoulder strap",
      'Dimensions: 12" x 14" x 5"',
    ],
    images: [
      "https://images.unsplash.com/photo-1584917865442-de89df76afd3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1591561954557-26941169b49e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: false,
    inStock: true,
    isFeatured: true,
  },
  {
    id: "handbag-2",
    name: "Classic Shoulder Bag",
    category: "handbags",
    price: 12499,
    description:
      "A timeless shoulder bag that combines style and functionality. Perfect for both casual and formal occasions.",
    features: [
      "Genuine leather",
      "Silver-tone hardware",
      "Multiple compartments",
      "Detachable shoulder strap",
      'Dimensions: 10" x 8" x 4"',
    ],
    images: [
      "https://images.unsplash.com/photo-1590874103328-eac38a683ce7?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1566150905458-1bf1fc113f0d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: false,
    inStock: true,
  },
  {
    id: "handbag-3",
    name: "Mini Crossbody Purse",
    category: "handbags",
    price: 7499,
    description:
      "A compact crossbody purse that's perfect for keeping your essentials close while on the go. Stylish and practical.",
    features: [
      "High-quality vegan leather",
      "Gold-tone chain strap",
      "Card slots and zip pocket",
      "Magnetic snap closure",
      'Dimensions: 7" x 5" x 2"',
    ],
    images: [
      "https://images.unsplash.com/photo-1601924994987-69e26d50dc26?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1600857062241-98e5dba7f214?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: false,
    inStock: true,
    isFeatured: true,
  },
  {
    id: "cap-1",
    name: "Classic Baseball Cap",
    category: "caps",
    price: 2899,
    description:
      "A timeless baseball cap with a clean design. Perfect for casual outings or protecting yourself from the sun in style.",
    features: [
      "100% cotton twill",
      "Adjustable back strap",
      "Embroidered eyelets",
      "Pre-curved visor",
      "One size fits most",
    ],
    images: [
      "https://images.unsplash.com/photo-1588850561407-ed78c282e89b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1620327467532-6ebaca6273ed?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: false,
    inStock: true,
  },
  {
    id: "cap-2",
    name: "Premium Snapback Cap",
    category: "caps",
    price: 3599,
    description:
      "A premium snapback cap with a flat brim and modern design. Make a statement with this trendy headwear option.",
    features: [
      "High-quality wool blend",
      "Snapback closure",
      "Flat brim",
      "Moisture-wicking sweatband",
      "One size fits most",
    ],
    images: [
      "https://images.unsplash.com/photo-1575428652377-a2d80e2277fc?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1556306535-0f09a537f0a3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: false,
    inStock: true,
    isFeatured: true,
  },
  {
    id: "cap-3",
    name: "Vintage Trucker Cap",
    category: "caps",
    price: 3249,
    description:
      "A vintage-inspired trucker cap with mesh panels for breathability. Classic style meets modern comfort.",
    features: [
      "Cotton front panel",
      "Mesh back panels",
      "Adjustable snapback",
      "Curved brim",
      "One size fits most",
    ],
    images: [
      "https://images.unsplash.com/photo-1534215754734-18e55d13e346?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1521369909029-2afed882baee?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: false,
    inStock: true,
  },
  {
    id: "custom-handbag-1",
    name: "Personalized Leather Tote",
    category: "custom",
    price: 16699,
    description:
      "A premium leather tote that can be customized with your name, initials, or a short message. Create a truly unique accessory that reflects your personal style.",
    features: [
      "100% genuine leather",
      "Custom monogramming or text",
      "Choice of hardware finish",
      "Multiple interior pockets",
      'Dimensions: 14" x 16" x 6"',
    ],
    images: [
      "https://images.unsplash.com/photo-1564422170194-896b89110ef8?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1590739225468-ac5ab957c79a?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: true,
    inStock: true,
    isFeatured: true,
  },
  {
    id: "custom-cap-1",
    name: "Custom Embroidered Cap",
    category: "custom",
    price: 4199,
    description:
      "A high-quality cap that can be customized with your choice of embroidered design, text, or logo. Stand out from the crowd with a cap that's uniquely yours.",
    features: [
      "Premium cotton construction",
      "Custom embroidery (up to 4 colors)",
      "Choice of cap color",
      "Adjustable strap",
      "One size fits most",
    ],
    images: [
      "https://images.unsplash.com/photo-1556306535-0f09a537f0a3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1595642527925-4d41cb781653?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: true,
    inStock: true,
    isFeatured: true,
  },
  // Hair Bands
  {
    id: "hairband-1",
    name: "Elegant Silk Hair Band",
    category: "hairbands",
    price: 1299,
    description:
      "A luxurious silk hair band that adds elegance to any hairstyle. Perfect for both casual and formal occasions.",
    features: [
      "100% pure silk",
      "Soft and gentle on hair",
      "Non-slip design",
      "One size fits all",
      "Available in multiple colors",
    ],
    images: [
      "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1583292650898-7d22cd27ca6f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: false,
    inStock: true,
    isFeatured: true,
  },
  {
    id: "hairband-2",
    name: "Embroidered Velvet Hair Band",
    category: "hairbands",
    price: 1599,
    description:
      "A beautiful velvet hair band with intricate embroidery work. Handcrafted with attention to detail.",
    features: [
      "Premium velvet material",
      "Hand-embroidered design",
      "Comfortable padding",
      "Adjustable fit",
      "Traditional Nepaln motifs",
    ],
    images: [
      "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1506629905607-c52b1b8b3b3b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: true,
    inStock: true,
    isFeatured: false,
  },
  // Earrings
  {
    id: "earrings-1",
    name: "Traditional Jhumka Earrings",
    category: "earrings",
    price: 2499,
    description:
      "Beautiful traditional jhumka earrings crafted with intricate designs. Perfect for festive occasions and cultural events.",
    features: [
      "Gold-plated finish",
      "Lightweight design",
      "Hypoallergenic materials",
      "Traditional Nepaln craftsmanship",
      "Secure hook closure",
    ],
    images: [
      "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: false,
    inStock: true,
    isFeatured: true,
  },
  {
    id: "earrings-2",
    name: "Contemporary Drop Earrings",
    category: "earrings",
    price: 1899,
    description:
      "Modern drop earrings with a contemporary design. Perfect for office wear and casual outings.",
    features: [
      "Sterling silver plated",
      "Minimalist design",
      "Comfortable for all-day wear",
      "Tarnish resistant",
      "Gift box included",
    ],
    images: [
      "https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: false,
    inStock: true,
    isFeatured: false,
  },
  // Necklaces
  {
    id: "necklace-1",
    name: "Elegant Pearl Necklace",
    category: "necklaces",
    price: 3999,
    description:
      "A timeless pearl necklace that adds sophistication to any outfit. Perfect for special occasions and formal events.",
    features: [
      "Cultured freshwater pearls",
      "Sterling silver clasp",
      "16-inch length",
      "Lustrous finish",
      "Comes with jewelry box",
    ],
    images: [
      "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: false,
    inStock: true,
    isFeatured: true,
  },
  {
    id: "necklace-2",
    name: "Traditional Kundan Necklace",
    category: "necklaces",
    price: 5499,
    description:
      "Exquisite traditional kundan necklace with intricate craftsmanship. Perfect for weddings and festive celebrations.",
    features: [
      "Authentic kundan work",
      "Gold-plated base",
      "Handcrafted design",
      "Adjustable chain length",
      "Traditional Nepaln artistry",
    ],
    images: [
      "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: true,
    inStock: true,
    isFeatured: true,
  },
  // Silk Pillow Covers
  {
    id: "pillowcover-1",
    name: "Luxury Silk Pillow Cover",
    category: "pillowcovers",
    price: 2299,
    description:
      "Premium mulberry silk pillow cover that's gentle on skin and hair. Perfect for a luxurious sleep experience.",
    features: [
      "100% mulberry silk",
      "Hypoallergenic material",
      "Temperature regulating",
      "Hidden zipper closure",
      "Standard size (20x30 inches)",
    ],
    images: [
      "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: false,
    inStock: true,
    isFeatured: true,
  },
  {
    id: "pillowcover-2",
    name: "Embroidered Silk Pillow Cover",
    category: "pillowcovers",
    price: 2799,
    description:
      "Elegant silk pillow cover with beautiful hand-embroidered patterns. Adds a touch of sophistication to your bedroom.",
    features: [
      "Pure silk with embroidery",
      "Handcrafted design",
      "Durable construction",
      "Easy care instructions",
      "Available in multiple colors",
    ],
    images: [
      "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
      "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    ],
    isCustomizable: true,
    inStock: true,
    isFeatured: false,
  },
];

export const getFeaturedProducts = (): Product[] => {
  return products.filter((product) => product.isFeatured);
};

export const getProductsByCategory = (category: string): Product[] => {
  if (category === "all") return products;
  return products.filter((product) => product.category === category);
};

export const getProductById = (id: string): Product | undefined => {
  return products.find((product) => product.id === id);
};
