// Test script to verify API integration
import { dataService, checkCmsConnection } from '@/lib/dataService'

export const testApiIntegration = async () => {
  console.log('🧪 Testing API Integration...')
  console.log('🔧 Environment Variables:')
  console.log('  VITE_USE_CMS_API:', import.meta.env.VITE_USE_CMS_API)
  console.log('  VITE_CMS_API_URL:', import.meta.env.VITE_CMS_API_URL)
  console.log('  VITE_FALLBACK_TO_STATIC:', import.meta.env.VITE_FALLBACK_TO_STATIC)

  // Test CMS connection first
  console.log('\n🔗 Testing CMS Connection...')
  const isConnected = await checkCmsConnection()
  console.log(`CMS Connection: ${isConnected ? '✅ Connected' : '❌ Failed'}`)

  try {
    // Test Products API
    console.log('\n📦 Testing Products API:')

    const featuredProducts = await dataService.products.getFeatured()
    console.log(`✅ Featured products: ${featuredProducts.length} items`)
    if (featuredProducts.length > 0) {
      console.log(`   First product: ${featuredProducts[0].name}`)
    }

    const allProducts = await dataService.products.getAll()
    console.log(`✅ All products: ${allProducts.products.length} items`)

    if (allProducts.products.length > 0) {
      const firstProduct = await dataService.products.getById(allProducts.products[0].id)
      console.log(`✅ Single product: ${firstProduct?.name}`)
    }

    // Test Blog API
    console.log('\n📝 Testing Blog API:')

    const publishedPosts = await dataService.blog.getPublished()
    console.log(`✅ Published posts: ${publishedPosts.length} items`)
    if (publishedPosts.length > 0) {
      console.log(`   First post: ${publishedPosts[0].title}`)
    }

    const blogCategories = await dataService.blog.getCategories()
    console.log(`✅ Blog categories: ${blogCategories.join(', ')}`)

    if (publishedPosts.length > 0) {
      const firstPost = await dataService.blog.getBySlug(publishedPosts[0].slug)
      console.log(`✅ Single post: ${firstPost?.title}`)
    }

    // Test Testimonials API
    console.log('\n💬 Testing Testimonials API:')

    const featuredTestimonials = await dataService.testimonials.getFeatured(3)
    console.log(`✅ Featured testimonials: ${featuredTestimonials.length} items`)
    if (featuredTestimonials.length > 0) {
      console.log(`   First testimonial: ${featuredTestimonials[0].name} (${featuredTestimonials[0].rating} stars)`)
    }

    const allTestimonials = await dataService.testimonials.getPublished()
    console.log(`✅ All testimonials: ${allTestimonials.length} items`)

    console.log('\n🎉 All API tests passed!')
    return true

  } catch (error) {
    console.error('❌ API test failed:', error)
    return false
  }
}

// Test direct API call
export const testDirectApiCall = async () => {
  console.log('🔍 Testing Direct API Call...')

  const apiUrl = import.meta.env.VITE_CMS_API_URL || 'http://localhost:3001/api'

  try {
    const response = await fetch(`${apiUrl}/products?limit=1`)
    console.log('Response status:', response.status)
    console.log('Response headers:', Object.fromEntries(response.headers.entries()))

    if (response.ok) {
      const data = await response.json()
      console.log('✅ Direct API call successful:', data)
      return true
    } else {
      console.error('❌ API response not ok:', response.statusText)
      return false
    }
  } catch (error) {
    console.error('❌ Direct API call failed:', error)
    return false
  }
}

// Auto-run test in development
if (import.meta.env.DEV) {
  // Run test after a short delay to allow app initialization
  setTimeout(() => {
    console.log('🚀 Starting automatic API integration test...')
    testApiIntegration()
    testDirectApiCall()
  }, 3000)
}
