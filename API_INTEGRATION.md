# CMS to Static Website API Integration

This document explains how the static website integrates with the CMS backend APIs.

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file in the static website root:

```env
# CMS API Configuration
VITE_USE_CMS_API=true
VITE_CMS_API_URL=http://localhost:3001/api
VITE_FALLBACK_TO_STATIC=true

# Development Settings
VITE_APP_ENV=development
VITE_DEBUG_API=true
```

### Configuration Options

- **`VITE_USE_CMS_API`**: Enable/disable CMS API usage
- **`VITE_CMS_API_URL`**: CMS API base URL
- **`VITE_FALLBACK_TO_STATIC`**: Fall back to static data if API fails

## 📡 API Service Layer

### Core Files

- **`src/lib/api.ts`**: Raw API functions and error handling
- **`src/lib/dataService.ts`**: Smart data service with fallbacks
- **`src/utils/testApi.ts`**: API integration testing

### Data Service Usage

```typescript
import { dataService } from '@/lib/dataService'

// Products
const featuredProducts = await dataService.products.getFeatured()
const productsByCategory = await dataService.products.getByCategory('handbags')
const singleProduct = await dataService.products.getById('product-id')

// Blog
const publishedPosts = await dataService.blog.getPublished()
const postsByCategory = await dataService.blog.getByCategory('fashion')
const singlePost = await dataService.blog.getBySlug('post-slug')
```

## 🔄 Fallback Strategy

The integration uses a smart fallback strategy:

1. **Primary**: Try CMS API
2. **Fallback**: Use static data if API fails
3. **Error**: Show error state if both fail

## 🚀 Updated Components

### Components Updated for API Integration

- **`FeaturedProducts.tsx`**: Now loads from CMS with loading states
- **`Products.tsx`**: Full API integration with filtering
- **`ProductDetail.tsx`**: Dynamic product loading
- **`Blog.tsx`**: Dynamic blog post listing
- **`BlogPost.tsx`**: Dynamic single post loading

### Loading States

All components now include:
- Loading spinners
- Error handling
- Retry functionality
- Graceful fallbacks

## 🧪 Testing

### Manual Testing

1. Start the CMS: `cd dreamy-duffel-cms && npm run dev`
2. Start the static site: `npm run dev`
3. Check browser console for API test results

### API Test Function

```typescript
import { testApiIntegration } from '@/utils/testApi'

// Run manual test
testApiIntegration()
```

## 🔧 Development Modes

### CMS API Mode
```env
VITE_USE_CMS_API=true
VITE_FALLBACK_TO_STATIC=true
```

### Static Data Only Mode
```env
VITE_USE_CMS_API=false
```

### Production Mode
```env
VITE_USE_CMS_API=true
VITE_CMS_API_URL=https://your-cms-domain.com/api
VITE_FALLBACK_TO_STATIC=false
```

## 📊 Performance Features

- **Caching**: 5-minute cache for API responses
- **Error Handling**: Comprehensive error boundaries
- **Loading States**: User-friendly loading indicators
- **Retry Logic**: Automatic retry on failures

## 🔍 Debugging

Enable debug mode:
```env
VITE_DEBUG_API=true
```

Check browser console for:
- API request logs
- Cache hit/miss information
- Error details
- Fallback triggers

## 🚦 Status Indicators

The integration provides visual feedback:
- ✅ **Loading**: Spinner with message
- ❌ **Error**: Error message with retry button
- 🔄 **Fallback**: Console warning when using static data

## 📈 Next Steps

1. **Deploy CMS**: Set up production CMS instance
2. **Update URLs**: Configure production API URLs
3. **Monitor**: Set up API monitoring and alerts
4. **Optimize**: Implement additional caching strategies
