'use client';

import { useEffect } from 'react';
import Link from 'next/link';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <div className="min-h-screen bg-brand-cream flex items-center justify-center py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto text-center">
          <div className="mb-8">
            <h1 className="font-serif text-4xl md:text-5xl font-bold text-brand-charcoal mb-4">
              Oops! Something went wrong
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              We're sorry, but something unexpected happened. Please try again or go back to the homepage.
            </p>
          </div>

          <div className="space-y-4 mb-8">
            <button
              onClick={reset}
              className="inline-block bg-brand-gold text-white px-8 py-3 rounded-md hover:opacity-90 transition-opacity font-medium mr-4"
            >
              Try Again
            </button>
            <Link
              href="/"
              className="inline-block bg-brand-charcoal text-white px-8 py-3 rounded-md hover:opacity-90 transition-opacity font-medium"
            >
              Go Home
            </Link>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h3 className="font-serif text-xl font-bold text-brand-charcoal mb-4">
              Need Help?
            </h3>
            <p className="text-gray-600 mb-4">
              If this problem persists, please contact our support team.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/products"
                className="text-brand-charcoal hover:text-brand-gold transition-colors font-medium"
              >
                Browse Products
              </Link>
              <Link
                href="/about"
                className="text-brand-charcoal hover:text-brand-gold transition-colors font-medium"
              >
                About Us
              </Link>
              <Link
                href="/blog"
                className="text-brand-charcoal hover:text-brand-gold transition-colors font-medium"
              >
                Read Our Blog
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
