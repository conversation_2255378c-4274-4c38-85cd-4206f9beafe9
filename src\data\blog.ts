export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  author: string;
  publishedAt: string;
  updatedAt: string;
  category: string;
  tags: string[];
  featuredImage: string;
  readTime: number;
  isPublished: boolean;
}

export const blogPosts: BlogPost[] = [
  {
    id: "1",
    title: "The Ultimate Guide to Choosing the Perfect Handbag",
    slug: "ultimate-guide-choosing-perfect-handbag",
    excerpt: "Discover how to select the ideal handbag that combines style, functionality, and quality. Our comprehensive guide covers everything from materials to sizing.",
    content: `
      <h2>Finding Your Perfect Handbag Match</h2>
      <p>Choosing the right handbag is more than just picking something that looks good – it's about finding a piece that complements your lifestyle, enhances your style, and serves your practical needs.</p>
      
      <h3>Consider Your Lifestyle</h3>
      <p>Before falling in love with a design, think about how you'll use your handbag. Are you a busy professional who needs space for a laptop and documents? A parent who requires easy access to essentials? Or someone who prefers to travel light with just the basics?</p>
      
      <h3>Material Matters</h3>
      <p>The material of your handbag determines both its durability and appearance. Genuine leather offers timeless elegance and improves with age, while synthetic materials provide affordability and often better weather resistance.</p>
      
      <h3>Size and Proportions</h3>
      <p>The size of your handbag should complement your body type and daily needs. A general rule is that your bag should be proportional to your frame – petite individuals often look best with smaller to medium bags, while taller people can carry larger styles with ease.</p>
      
      <h3>Quality Indicators</h3>
      <p>Look for sturdy hardware, reinforced stitching, and quality zippers. These details indicate a well-made bag that will last for years. At Dreamy Duffel, we ensure every piece meets our high standards for craftsmanship.</p>
    `,
    author: "Priya Sharma",
    publishedAt: "2024-12-15",
    updatedAt: "2024-12-15",
    category: "Style Guide",
    tags: ["handbags", "fashion", "style tips", "accessories"],
    featuredImage: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    readTime: 5,
    isPublished: true
  },
  {
    id: "2",
    title: "Custom Embroidery: Making Your Cap Uniquely Yours",
    slug: "custom-embroidery-making-cap-uniquely-yours",
    excerpt: "Learn about the art of custom embroidery and how personalized caps can express your individual style and make perfect gifts.",
    content: `
      <h2>The Art of Personalization</h2>
      <p>Custom embroidery transforms an ordinary cap into a personal statement. Whether you're representing your business, commemorating an event, or simply expressing your personality, embroidered caps offer endless possibilities.</p>
      
      <h3>Design Considerations</h3>
      <p>When creating your custom design, consider the cap's color, the thread colors available, and the size limitations. Simple, bold designs often work best for embroidery, ensuring clarity and impact.</p>
      
      <h3>Popular Customization Options</h3>
      <ul>
        <li>Company logos and branding</li>
        <li>Personal initials or names</li>
        <li>Motivational quotes or slogans</li>
        <li>Special dates or commemorative text</li>
        <li>Sports team affiliations</li>
      </ul>
      
      <h3>Care for Embroidered Caps</h3>
      <p>To maintain the quality of your custom embroidery, hand wash when possible and avoid harsh detergents. Store your cap properly to prevent the embroidery from snagging or distorting.</p>
    `,
    author: "Rajesh Kumar",
    publishedAt: "2024-12-10",
    updatedAt: "2024-12-10",
    category: "Customization",
    tags: ["caps", "custom design", "embroidery", "personalization"],
    featuredImage: "https://images.unsplash.com/photo-1556306535-0f09a537f0a3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    readTime: 4,
    isPublished: true
  },
  {
    id: "3",
    title: "Sustainable Fashion: Our Commitment to Eco-Friendly Accessories",
    slug: "sustainable-fashion-eco-friendly-accessories",
    excerpt: "Discover how Dreamy Duffel is committed to sustainable practices and eco-friendly materials in creating beautiful, responsible fashion accessories.",
    content: `
      <h2>Fashion with a Conscience</h2>
      <p>At Dreamy Duffel, we believe that beautiful accessories shouldn't come at the cost of our planet. Our commitment to sustainability drives every decision we make, from material selection to packaging.</p>
      
      <h3>Sustainable Materials</h3>
      <p>We prioritize eco-friendly materials including recycled fabrics, organic cotton, and responsibly sourced leather alternatives. These materials reduce environmental impact while maintaining the quality and durability our customers expect.</p>
      
      <h3>Ethical Manufacturing</h3>
      <p>Our manufacturing partners share our values of fair labor practices and environmental responsibility. We work closely with suppliers who provide safe working conditions and fair wages.</p>
      
      <h3>Reducing Waste</h3>
      <p>Through careful planning and efficient production processes, we minimize waste in our manufacturing. Leftover materials are repurposed into smaller accessories or donated to local craft programs.</p>
      
      <h3>Packaging Innovation</h3>
      <p>Our packaging uses recycled and biodegradable materials. We've eliminated unnecessary plastic and designed our packaging to be reusable, encouraging customers to give it a second life.</p>
    `,
    author: "Anita Desai",
    publishedAt: "2024-12-05",
    updatedAt: "2024-12-05",
    category: "Sustainability",
    tags: ["sustainability", "eco-friendly", "ethical fashion", "environment"],
    featuredImage: "https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60",
    readTime: 6,
    isPublished: true
  }
];

export const getBlogPosts = (): BlogPost[] => {
  return blogPosts.filter(post => post.isPublished);
};

export const getBlogPostBySlug = (slug: string): BlogPost | undefined => {
  return blogPosts.find(post => post.slug === slug && post.isPublished);
};

export const getBlogPostsByCategory = (category: string): BlogPost[] => {
  return blogPosts.filter(post => post.category === category && post.isPublished);
};

export const getBlogCategories = (): string[] => {
  const categories = blogPosts.map(post => post.category);
  return [...new Set(categories)];
};
