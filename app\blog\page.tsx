import { Metadata } from 'next';
import BlogClient from './blog-client';

// Enable SSR for better SEO
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: 'Blog - Fashion Tips & Style Guides | Dreamy Duffel',
  description: 'Discover the latest fashion trends, styling tips, and accessory guides from Dreamy Duffel. Learn how to style handbags, caps, and accessories for every occasion.',
  keywords: 'fashion blog, style tips, accessory guides, handbag styling, cap fashion, Dreamy Duffel blog, fashion trends Nepal',
  openGraph: {
    title: 'Blog - Fashion Tips & Style Guides | Dreamy Duffel',
    description: 'Discover the latest fashion trends, styling tips, and accessory guides from Dreamy Duffel. Learn how to style handbags, caps, and accessories for every occasion.',
    url: 'https://dreamyduffles.shop/blog',
  },
  alternates: {
    canonical: 'https://dreamyduffles.shop/blog',
  },
};

export default function BlogPage() {
  return <BlogClient />;
}
