
import Link from "next/link";

const CategorySection = () => {
  const categories = [
    {
      name: "Handbags",
      slug: "handbags",
      description: "Elegant and functional handbags for every occasion.",
      image: "https://images.unsplash.com/photo-1584917865442-de89df76afd3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60"
    },
    {
      name: "Caps",
      slug: "caps",
      description: "Stylish caps to complete your look with confidence.",
      image: "https://images.unsplash.com/photo-1575428652377-a2d80e2277fc?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=60"
    },
    {
      name: "Hair Bands",
      slug: "hairbands",
      description: "Beautiful hair accessories for every hairstyle.",
      image: "https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=60"
    },
    {
      name: "Earrings",
      slug: "earrings",
      description: "Exquisite earrings to enhance your natural beauty.",
      image: "https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=60"
    },
    {
      name: "Necklaces",
      slug: "necklaces",
      description: "Elegant necklaces for special occasions.",
      image: "https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=60"
    },
    {
      name: "Silk Pillow Covers",
      slug: "pillowcovers",
      description: "Luxurious silk pillow covers for better sleep.",
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=60"
    },
    {
      name: "Custom Designs",
      slug: "custom",
      description: "Personalized items uniquely crafted for you.",
      image: "https://images.unsplash.com/photo-1643190919067-6aab5356bc85?q=80&w=1432&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="font-serif text-3xl md:text-4xl font-bold text-brand-charcoal mb-4">
            Shop By Category
          </h2>
          <p className="max-w-2xl mx-auto text-gray-600">
            Browse our collections by category to find the perfect style for you.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {categories.map((category) => (
            <div key={category.slug} className="relative group overflow-hidden rounded-lg shadow-md">
              <div className="absolute inset-0 bg-black opacity-40 group-hover:opacity-30 transition-opacity"></div>
              <img
                src={category.image}
                alt={category.name}
                className="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-105"
              />
              <div className="absolute inset-0 flex flex-col items-center justify-center text-white p-4">
                <h3 className="font-serif text-2xl font-bold mb-3 text-center">{category.name}</h3>
                <p className="text-center mb-4 max-w-xs text-sm">{category.description}</p>
                <Link
                  href={`/products?category=${category.slug}`}
                  className="px-4 py-2 border-2 border-white hover:bg-white hover:text-brand-charcoal transition-colors text-sm"
                >
                  Shop Now
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CategorySection;
