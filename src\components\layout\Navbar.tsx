
'use client';

import { useState, useEffect, useRef } from "react";
import Link from "next/link";
import { ShoppingBag, Menu, X, Search, User, Heart, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <nav className="bg-white shadow-sm">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="font-serif text-2xl font-bold text-brand-charcoal">
            Dreamy Duffel
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-brand-charcoal hover:text-brand-gold transition-colors">
              Home
            </Link>
            <Link href="/products" className="text-brand-charcoal hover:text-brand-gold transition-colors">
              Products
            </Link>
            <div className="relative" ref={dropdownRef}>
              <button
                className="text-brand-charcoal hover:text-brand-gold transition-colors flex items-center gap-1"
                onMouseEnter={() => setIsDropdownOpen(true)}
                onMouseLeave={() => setIsDropdownOpen(false)}
                onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              >
                Categories
                <ChevronDown size={16} className={`transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
              </button>
              <div
                className={`absolute left-0 mt-2 w-48 bg-white shadow-lg rounded-md overflow-hidden z-50 transition-all duration-300 ${
                  isDropdownOpen ? 'opacity-100 visible translate-y-0' : 'opacity-0 invisible -translate-y-2'
                }`}
                onMouseEnter={() => setIsDropdownOpen(true)}
                onMouseLeave={() => setIsDropdownOpen(false)}
              >
                <Link
                  href="/products?category=handbags"
                  className="block px-4 py-2 text-sm text-brand-charcoal hover:bg-brand-cream transition-colors"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  Handbags
                </Link>
                <Link
                  href="/products?category=caps"
                  className="block px-4 py-2 text-sm text-brand-charcoal hover:bg-brand-cream transition-colors"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  Caps
                </Link>
                <Link
                  href="/products?category=hairbands"
                  className="block px-4 py-2 text-sm text-brand-charcoal hover:bg-brand-cream transition-colors"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  Hair Bands
                </Link>
                <Link
                  href="/products?category=earrings"
                  className="block px-4 py-2 text-sm text-brand-charcoal hover:bg-brand-cream transition-colors"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  Earrings
                </Link>
                <Link
                  href="/products?category=necklaces"
                  className="block px-4 py-2 text-sm text-brand-charcoal hover:bg-brand-cream transition-colors"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  Necklaces
                </Link>
                <Link
                  href="/products?category=pillowcovers"
                  className="block px-4 py-2 text-sm text-brand-charcoal hover:bg-brand-cream transition-colors"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  Silk Pillow Covers
                </Link>
                <Link
                  href="/products?category=custom"
                  className="block px-4 py-2 text-sm text-brand-charcoal hover:bg-brand-cream transition-colors"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  Customized Items
                </Link>
              </div>
            </div>
            <Link href="/blog" className="text-brand-charcoal hover:text-brand-gold transition-colors">
              Blog
            </Link>
            <Link href="/about" className="text-brand-charcoal hover:text-brand-gold transition-colors">
              About Us
            </Link>
          </div>

          {/* Icons */}
          <div className="hidden md:flex items-center space-x-4">
            <button className="text-brand-charcoal hover:text-brand-gold transition-colors">
              <Search size={20} />
            </button>
            <button className="text-brand-charcoal hover:text-brand-gold transition-colors">
              <User size={20} />
            </button>
            <button className="text-brand-charcoal hover:text-brand-gold transition-colors">
              <Heart size={20} />
            </button>
            <button className="text-brand-charcoal hover:text-brand-gold transition-colors relative">
              <ShoppingBag size={20} />
              <span className="absolute -top-1 -right-1 bg-brand-gold text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                0
              </span>
            </button>
          </div>

          {/* Mobile Menu Button */}
          <div className="flex md:hidden items-center space-x-4">
            <button className="text-brand-charcoal hover:text-brand-gold transition-colors relative">
              <ShoppingBag size={20} />
              <span className="absolute -top-1 -right-1 bg-brand-gold text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                0
              </span>
            </button>
            <button
              className="text-brand-charcoal hover:text-brand-gold transition-colors"
              onClick={toggleMenu}
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        <div className={`md:hidden ${isMenuOpen ? 'block' : 'hidden'} pt-4`}>
          <div className="flex flex-col space-y-3">
            <Link
              href="/"
              className="text-brand-charcoal hover:text-brand-gold transition-colors py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              Home
            </Link>
            <Link
              href="/products"
              className="text-brand-charcoal hover:text-brand-gold transition-colors py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              Products
            </Link>
            <Link
              href="/products?category=handbags"
              className="text-brand-charcoal hover:text-brand-gold transition-colors py-2 pl-3 text-sm"
              onClick={() => setIsMenuOpen(false)}
            >
              Handbags
            </Link>
            <Link
              href="/products?category=caps"
              className="text-brand-charcoal hover:text-brand-gold transition-colors py-2 pl-3 text-sm"
              onClick={() => setIsMenuOpen(false)}
            >
              Caps
            </Link>
            <Link
              href="/products?category=hairbands"
              className="text-brand-charcoal hover:text-brand-gold transition-colors py-2 pl-3 text-sm"
              onClick={() => setIsMenuOpen(false)}
            >
              Hair Bands
            </Link>
            <Link
              href="/products?category=earrings"
              className="text-brand-charcoal hover:text-brand-gold transition-colors py-2 pl-3 text-sm"
              onClick={() => setIsMenuOpen(false)}
            >
              Earrings
            </Link>
            <Link
              href="/products?category=necklaces"
              className="text-brand-charcoal hover:text-brand-gold transition-colors py-2 pl-3 text-sm"
              onClick={() => setIsMenuOpen(false)}
            >
              Necklaces
            </Link>
            <Link
              href="/products?category=pillowcovers"
              className="text-brand-charcoal hover:text-brand-gold transition-colors py-2 pl-3 text-sm"
              onClick={() => setIsMenuOpen(false)}
            >
              Silk Pillow Covers
            </Link>
            <Link
              href="/products?category=custom"
              className="text-brand-charcoal hover:text-brand-gold transition-colors py-2 pl-3 text-sm"
              onClick={() => setIsMenuOpen(false)}
            >
              Customized Items
            </Link>
            <Link
              href="/blog"
              className="text-brand-charcoal hover:text-brand-gold transition-colors py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              Blog
            </Link>
            <Link
              href="/about"
              className="text-brand-charcoal hover:text-brand-gold transition-colors py-2"
              onClick={() => setIsMenuOpen(false)}
            >
              About Us
            </Link>
            <div className="pt-2 flex space-x-4">
              <button className="text-brand-charcoal hover:text-brand-gold transition-colors">
                <Search size={20} />
              </button>
              <button className="text-brand-charcoal hover:text-brand-gold transition-colors">
                <User size={20} />
              </button>
              <button className="text-brand-charcoal hover:text-brand-gold transition-colors">
                <Heart size={20} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
