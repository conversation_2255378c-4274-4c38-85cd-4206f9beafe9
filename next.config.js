/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable image optimization for better SEO and performance
  images: {
    domains: ['dreamyduffles.shop', 'images.unsplash.com'],
    formats: ['image/webp', 'image/avif'],
  },
  // Enable compression
  compress: true,
  // Disable ESLint during build (temporary)
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable TypeScript checking during build (temporary)
  typescript: {
    ignoreBuildErrors: true,
  },
  // Configure headers for better SEO and security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
  // Configure redirects for SEO
  async redirects() {
    return [
      {
        source: '/home',
        destination: '/',
        permanent: true,
      },
    ];
  },
};

module.exports = nextConfig;
