import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { dataService } from "@/lib/dataService";
import BlogPostClient from './blog-post-client';

// Enable SSR for better SEO
export const dynamic = 'force-dynamic';

interface BlogPostPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params;
  try {
    const post = await dataService.blog.getBySlug(slug);
    
    if (!post) {
      return {
        title: 'Blog Post Not Found | Dreamy Duffel',
        description: 'The blog post you are looking for could not be found.',
      };
    }

    return {
      title: `${post.title} | Dreamy Duffel Blog`,
      description: post.excerpt,
      keywords: `${post.tags?.join(', ')}, fashion blog, style tips, Dreamy Duffel`,
      openGraph: {
        title: `${post.title} | Dreamy Duffel Blog`,
        description: post.excerpt,
        url: `https://dreamyduffles.shop/blog/${slug}`,
        images: [
          {
            url: post.featuredImage,
            width: 1200,
            height: 630,
            alt: post.title,
          },
        ],
        type: 'article',
        publishedTime: post.publishedAt,
        modifiedTime: post.updatedAt,
        authors: [post.author],
        section: post.category,
        tags: post.tags,
      },
      twitter: {
        title: `${post.title} | Dreamy Duffel Blog`,
        description: post.excerpt,
        images: [post.featuredImage],
      },
      alternates: {
        canonical: `https://dreamyduffles.shop/blog/${slug}`,
      },
    };
  } catch (error) {
    return {
      title: 'Blog Post Not Found | Dreamy Duffel',
      description: 'The blog post you are looking for could not be found.',
    };
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;

  try {
    const post = await dataService.blog.getBySlug(slug);

    if (!post) {
      notFound();
    }

    // Get related posts
    const relatedPosts = await dataService.blog.getByCategory(post.category);
    const filteredRelatedPosts = relatedPosts.filter(p => p.slug !== slug).slice(0, 3);

    return (
      <BlogPostClient 
        post={post} 
        relatedPosts={filteredRelatedPosts}
      />
    );
  } catch (error) {
    console.error('Failed to load blog post:', error);
    notFound();
  }
}
